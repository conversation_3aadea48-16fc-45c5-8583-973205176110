using UnityEngine;
using UnityEngine.UI;

public class SkillTabButton : MonoBehaviour
{
    public SkillTabs manager;

    void Awake()
    {
        this.GetComponent<Button>().onClick.AddListener(OnClick);
    }

    public void OnClick()
    {
        manager.OnTabClicked(GetCurrentIndex());
    }

    private int GetCurrentIndex()
    {
        for (int i = 0; i < manager.Tabs.Length; i++)
        {
            if (manager.Tabs[i] == transform as RectTransform)
                return i;
        }

        Debug.LogWarning("Tab not found in manager's list!");
        return -1;
    }
}
