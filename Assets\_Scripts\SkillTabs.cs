using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class SkillTabs : MonoBehaviour
{
    [SerializeField] private RectTransform rotationCenter;
    [SerializeField] private RectTransform[] Placeholders;
    [SerializeField] public RectTransform[] Tabs;

    float minScale = 0.6f;
    private int middleIndex;
    Vector2[] originalPositions;
    Vector3[] originalScales;
    bool useClockwiseDirection = true;

    void Awake()
    {
        originalPositions = new Vector2[Tabs.Length];
        originalScales = new Vector3[Tabs.Length];

        for (int i = 0; i < Tabs.Length; i++)
        {
            originalPositions[i] = Tabs[i].anchoredPosition;
            originalScales[i] = Tabs[i].localScale;
        }

        middleIndex = Tabs.Length / 2;
    }

    public void InitializeTabs(float duration = 0.3f)
    {
        float scaleStep = (1.0f - minScale) / middleIndex;

        for (int i = 0; i < Tabs.Length; i++)
        {
            int distanceFromMiddle = Mathf.Abs(i - middleIndex);
            float scale = 1.0f - (distanceFromMiddle * scaleStep);

            Vector2 targetPos = Placeholders[i].anchoredPosition;
            StartCoroutine(LerpTab(Tabs[i], targetPos, new Vector3(scale, scale, 1f), duration));
        }
    }

    public void OnTabClicked(int clickedIndex, float duration = 0.3f)
    {
        if (clickedIndex == middleIndex)
            return;

        int clockwiseDistance = (middleIndex - clickedIndex + Placeholders.Length) % Placeholders.Length;
        int counterclockwiseDistance = (clickedIndex - middleIndex + Placeholders.Length) % Placeholders.Length;

        useClockwiseDirection = clockwiseDistance <= counterclockwiseDistance;

        Debug.Log($"clickedIndex {clickedIndex} middleIndex {middleIndex}. clockwiseDistance {clockwiseDistance} counterclockwiseDistance  {counterclockwiseDistance}   useClockwiseDirection {(useClockwiseDirection ? "clockwise" : "counterclockwise")}");

        int shiftAmount = middleIndex - clickedIndex;

        Tabs = RotateArray(Tabs, shiftAmount);

        InitializeTabs(duration);
    }

    public void ResetTabs(float duration = 0.3f)
    {
        for (int i = 0; i < Tabs.Length; i++)
        {
            StartCoroutine(LerpTab(Tabs[i], originalPositions[i], originalScales[i], duration));
        }
    }

    IEnumerator LerpTab(RectTransform tab, Vector2 targetPos, Vector3 targetScale, float duration)
    {
        Vector2 startPos = tab.anchoredPosition;
        Vector3 startScale = tab.localScale;
        Vector2 center = rotationCenter.anchoredPosition;

        List<Vector2> pathPositions = GetCircularPath(startPos, targetPos);
        // Debug.Log($"Path positions: {string.Join(", ", pathPositions)}");

        float time = 0f;
        int currentSegment = 0;
        float segmentDuration = duration / (pathPositions.Count - 1);

        while (time < duration && currentSegment < pathPositions.Count - 1)
        {
            float segmentTime = (time - currentSegment * segmentDuration) / segmentDuration;
            segmentTime = Mathf.Clamp01(segmentTime);

            Vector2 segmentStart = pathPositions[currentSegment];
            Vector2 segmentEnd = pathPositions[currentSegment + 1];

            // Convert segment positions to polar coords for circular interpolation
            float startAngle = Mathf.Atan2(segmentStart.y - center.y, segmentStart.x - center.x);
            float endAngle = Mathf.Atan2(segmentEnd.y - center.y, segmentEnd.x - center.x);

            float startRadius = Vector2.Distance(segmentStart, center);
            float endRadius = Vector2.Distance(segmentEnd, center);

            // Calculate the full circular path (not shortest)
            float angleDiff = endAngle - startAngle;

            // Normalize angle difference to ensure we go the full way around
            if (Mathf.Abs(angleDiff) > Mathf.PI)
            {
                if (angleDiff > 0)
                    angleDiff -= 2 * Mathf.PI;
                else
                    angleDiff += 2 * Mathf.PI;
            }

            float currentAngle = startAngle + angleDiff * segmentTime;
            float currentRadius = Mathf.Lerp(startRadius, endRadius, segmentTime);

            float x = center.x + Mathf.Cos(currentAngle) * currentRadius;
            float y = center.y + Mathf.Sin(currentAngle) * currentRadius;

            tab.anchoredPosition = new Vector2(x, y);

            // scale
            float totalProgress = time / duration;
            tab.localScale = Vector3.Lerp(startScale, targetScale, totalProgress);

            time += Time.deltaTime;

            // step next segment
            if (segmentTime >= 1f)
            {
                currentSegment++;
            }

            yield return null;
        }

        // Snap to final target
        tab.anchoredPosition = targetPos;
        tab.localScale = targetScale;
    }

    private List<Vector2> GetCircularPath(Vector2 startPos, Vector2 targetPos)
    {
        List<Vector2> path = new List<Vector2> { startPos };
        int startIndex = -1;
        int targetIndex = -1;

        for (int i = 0; i < Placeholders.Length; i++)
        {
            if (Vector2.Distance(startPos, Placeholders[i].anchoredPosition) < 1f)
                startIndex = i;
            if (Vector2.Distance(targetPos, Placeholders[i].anchoredPosition) < 1f)
                targetIndex = i;
        }

        // Debug.Log($"startIndex {startIndex} targetIndex {targetIndex}, useClockwiseDirection {(useClockwiseDirection ? "clockwise" : "counterclockwise")}");

        if (startIndex != -1 && targetIndex != -1 && startIndex != targetIndex)
        {
            int current = startIndex;
            while (current != targetIndex)
            {
                if (useClockwiseDirection)
                {
                    current = (current + 1) % Placeholders.Length;
                }
                else
                {
                    current = (current - 1 + Placeholders.Length) % Placeholders.Length;
                }
                path.Add(Placeholders[current].anchoredPosition);
            }
        }
        else
        {
            path.Add(targetPos);
        }

        return path;
    }

    public void TabClicked(int index)
    {
        OnTabClicked(index);
    }

    private T[] RotateArray<T>(T[] array, int shiftAmount)
    {
        int n = array.Length;
        T[] rotated = new T[n];

        for (int i = 0; i < n; i++)
        {
            int newIndex = (i + shiftAmount + n) % n;
            rotated[newIndex] = array[i];
        }

        return rotated;
    }

    void OnDrawGizmos()
    {
        if (Placeholders == null || rotationCenter == null) return;

        Gizmos.color = Color.red;
        Gizmos.DrawSphere(rotationCenter.position, 10f);

        Gizmos.color = Color.green;
        foreach (var ph in Placeholders)
        {
            Gizmos.DrawLine(rotationCenter.position, ph.position);
            Gizmos.DrawSphere(ph.position, 5f);
        }
    }

}
